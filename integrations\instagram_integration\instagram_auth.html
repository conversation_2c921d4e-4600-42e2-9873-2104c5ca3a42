<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Integration - Account Connection</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #833ab4, #fd1d1d, #fcb045);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #833ab4, #fd1d1d);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .step {
            margin-bottom: 30px;
            padding: 25px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
            background: #fafafa;
        }

        .step h3 {
            color: #833ab4;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input[type="text"]#twoFactorCode {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
        }

        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #833ab4;
        }

        button {
            background: linear-gradient(45deg, #833ab4, #fd1d1d);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        button:hover {
            transform: translateY(-2px);
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
            margin: 15px 0;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            margin: 15px 0;
        }

        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
            margin: 15px 0;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #bee5eb;
            margin: 15px 0;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected { background: #28a745; }
        .status-disconnected { background: #dc3545; }
        .status-pending { background: #ffc107; }

        .account-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .limitation-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .limitation-note h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .limitation-note ul {
            margin-left: 20px;
        }

        .limitation-note li {
            margin-bottom: 5px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📷 Instagram Integration</h1>
            <p>Connect your Instagram Business/Creator account for comment replies and story interactions</p>
        </div>

        <div class="content">
            <!-- Instagram Limitations Notice -->
            <div class="limitation-note">
                <h4>📋 Instagram Messaging Limitations</h4>
                <ul>
                    <li><strong>Business/Creator accounts only:</strong> Personal accounts cannot use Instagram messaging APIs</li>
                    <li><strong>Limited direct messaging:</strong> Focus on comment replies and story interactions</li>
                    <li><strong>24-hour rule:</strong> Can only message users who contacted you within 24 hours</li>
                    <li><strong>No bulk messaging:</strong> Instagram has strict anti-spam policies</li>
                    <li><strong>Comment replies recommended:</strong> Most reliable way to engage with your audience</li>
                </ul>
            </div>

            <!-- Step 1: Connection Status -->
            <div class="step">
                <h3>Step 1: Connection Status</h3>
                <div id="connectionStatus">
                    <span class="status-indicator status-pending"></span>
                    <span>Checking connection...</span>
                </div>
                <button type="button" onclick="checkConnectionStatus()">🔄 Refresh Status</button>
                <div id="statusResult"></div>
            </div>

            <!-- Step 2: Account Connection -->
            <div class="step">
                <h3>Step 2: Connect Instagram Account</h3>
                <p><strong>Requirements:</strong> Instagram Business or Creator account with valid credentials</p>



                <div class="form-group">
                    <label for="username">Instagram Username/Email:</label>
                    <input type="text" id="username" placeholder="your_business_<NAME_EMAIL>">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" placeholder="Your Instagram password">
                </div>
                <div class="form-group" id="twoFactorGroup" style="display: none;">
                    <label for="twoFactorCode">2FA Code:</label>
                    <input type="text" id="twoFactorCode" placeholder="Enter 6-digit code from your authenticator app" maxlength="6">
                    <small>Enter the 6-digit code from your authenticator app or SMS</small>
                </div>
                <button type="button" onclick="connectAccount()" id="connectBtn">🔗 Connect Account</button>
                <button type="button" onclick="submitTwoFactor()" id="twoFactorBtn" style="display: none;">🔐 Submit 2FA Code</button>
                <div id="connectionResult"></div>
            </div>

            <!-- Step 3: Account Information -->
            <div class="step">
                <h3>Step 3: Account Information</h3>
                <button type="button" onclick="getAccountInfo()">📊 Get Account Info</button>
                <button type="button" onclick="show2FAGuidance()" style="background: linear-gradient(45deg, #ff6b6b, #ffa726); margin-left: 10px;">🔐 2FA Help</button>
                <div id="accountInfo"></div>
            </div>

            <!-- Step 4: Comment Management -->
            <div class="step">
                <h3>Step 4: Comment Interactions</h3>
                <p><strong>Primary Instagram feature:</strong> Reply to comments on your posts</p>

                <h4>Reply to Comment</h4>
                <div class="form-group">
                    <label for="commentId">Comment ID:</label>
                    <input type="text" id="commentId" placeholder="Instagram comment ID">
                </div>
                <div class="form-group">
                    <label for="commentReply">Reply Text:</label>
                    <textarea id="commentReply" rows="3" placeholder="Thanks for your comment! 😊"></textarea>
                </div>
                <button type="button" onclick="replyToComment()">💬 Reply to Comment</button>
                <div id="commentReplyResult"></div>

                <h4>Get Post Comments</h4>
                <div class="form-group">
                    <label for="postId">Post/Media ID:</label>
                    <input type="text" id="postId" placeholder="Instagram post/media ID">
                </div>
                <button type="button" onclick="getPostComments()">📝 Get Comments</button>
                <div id="commentsResult"></div>
            </div>

            <!-- Step 5: Story Interactions -->
            <div class="step">
                <h3>Step 5: Story Interactions</h3>
                <p><strong>Story replies:</strong> Respond to story mentions and interactions</p>

                <div class="form-group">
                    <label for="storyId">Story ID:</label>
                    <input type="text" id="storyId" placeholder="Instagram story ID">
                </div>
                <div class="form-group">
                    <label for="storyReply">Story Reply:</label>
                    <textarea id="storyReply" rows="2" placeholder="Thanks for sharing! 🙌"></textarea>
                </div>
                <button type="button" onclick="replyToStory()">📱 Reply to Story</button>
                <div id="storyReplyResult"></div>
            </div>

            <!-- Step 6: Conversations -->
            <div class="step">
                <h3>Step 6: View Conversations</h3>
                <p><strong>Limited access:</strong> View existing conversations (subject to Instagram restrictions)</p>
                
                <button type="button" onclick="getConversations()">💬 Get Conversations</button>
                <div id="conversationsResult"></div>
            </div>

            <!-- Step 7: Best Practices -->
            <div class="step">
                <h3>Step 7: Instagram Best Practices</h3>
                <ul>
                    <li><strong>Focus on engagement:</strong> Use comment replies to build community</li>
                    <li><strong>Respond quickly:</strong> Reply to comments and stories promptly</li>
                    <li><strong>Be authentic:</strong> Personal responses perform better than automated ones</li>
                    <li><strong>Follow guidelines:</strong> Adhere to Instagram's community standards</li>
                    <li><strong>Monitor regularly:</strong> Check for new comments and story mentions</li>
                    <li><strong>Use hashtags wisely:</strong> Engage with relevant hashtag conversations</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh connection status every 30 seconds
        setInterval(checkConnectionStatus, 30000);

        // Check connection status on page load
        window.onload = function() {
            checkConnectionStatus();
        };

        function checkConnectionStatus() {
            fetch('/api/instagram/connection-status')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('connectionStatus');
                    const resultDiv = document.getElementById('statusResult');

                    if (data.success && data.status) {
                        const unipileStatus = data.status.unipile;
                        
                        if (unipileStatus.connected && unipileStatus.accounts.length > 0) {
                            statusDiv.innerHTML = '<span class="status-indicator status-connected"></span><span>✅ Connected to Instagram</span>';
                            
                            let accountsHtml = '<div class="account-info"><h4>Connected Accounts:</h4>';
                            unipileStatus.accounts.forEach(account => {
                                accountsHtml += `<p>📷 ${account.username} (${account.name})</p>`;
                            });
                            accountsHtml += '</div>';
                            resultDiv.innerHTML = accountsHtml;
                        } else {
                            statusDiv.innerHTML = '<span class="status-indicator status-disconnected"></span><span>❌ Not connected to Instagram</span>';
                            resultDiv.innerHTML = '<div class="info">Please connect your Instagram account using the form below.</div>';
                        }
                    } else {
                        statusDiv.innerHTML = '<span class="status-indicator status-disconnected"></span><span>❌ Connection check failed</span>';
                        resultDiv.innerHTML = '<div class="error">Unable to check connection status. Please try again.</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('connectionStatus').innerHTML = '<span class="status-indicator status-disconnected"></span><span>❌ Connection error</span>';
                    document.getElementById('statusResult').innerHTML = '<div class="error">Error checking status: ' + error.message + '</div>';
                });
        }

        let currentConnectionData = null; // Store connection data for 2FA

        function connectAccount() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('connectionResult');

            if (!username || !password) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter both username and password</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">🔄 Connecting Instagram account...</div>';

            fetch('/api/instagram/connect-account', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Instagram account connected successfully!<br>
                            Account ID: ${data.account_id}<br>
                            <small>You can now use comment replies and story interactions.</small>
                        </div>
                    `;
                    // Refresh status after successful connection
                    setTimeout(checkConnectionStatus, 1000);
                } else if (data.requires_2fa || (data.error && (data.error.includes('2FA') || data.error.includes('two-factor') || data.error.includes('checkpoint')))) {
                    // Handle 2FA requirement
                    currentConnectionData = {
                        username: username,
                        password: password,
                        challenge_id: data.challenge_id || null
                    };

                    show2FAInput();
                    resultDiv.innerHTML = `
                        <div class="warning">
                            🔐 Two-Factor Authentication Required<br>
                            Please enter the 6-digit code from your authenticator app or SMS below.
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ Connection failed: ${data.error || data.message}<br>
                            <small>Please ensure you're using a Business or Creator account with correct credentials.</small>
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Connection error: ' + error.message + '</div>';
            });
        }

        function show2FAInput() {
            // Show 2FA input field and button
            document.getElementById('twoFactorGroup').style.display = 'block';
            document.getElementById('twoFactorBtn').style.display = 'inline-block';
            document.getElementById('connectBtn').style.display = 'none';

            // Focus on 2FA input
            document.getElementById('twoFactorCode').focus();
        }

        function hide2FAInput() {
            // Hide 2FA input field and button
            document.getElementById('twoFactorGroup').style.display = 'none';
            document.getElementById('twoFactorBtn').style.display = 'none';
            document.getElementById('connectBtn').style.display = 'inline-block';

            // Clear 2FA code
            document.getElementById('twoFactorCode').value = '';
            currentConnectionData = null;
        }

        function submitTwoFactor() {
            const twoFactorCode = document.getElementById('twoFactorCode').value;
            const resultDiv = document.getElementById('connectionResult');

            if (!twoFactorCode || twoFactorCode.length !== 6) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter a valid 6-digit 2FA code</div>';
                return;
            }

            if (!currentConnectionData) {
                resultDiv.innerHTML = '<div class="error">❌ Connection data lost. Please try connecting again.</div>';
                hide2FAInput();
                return;
            }

            resultDiv.innerHTML = '<div class="info">🔄 Verifying 2FA code...</div>';

            fetch('/api/instagram/verify-2fa', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: currentConnectionData.username,
                    password: currentConnectionData.password,
                    two_factor_code: twoFactorCode,
                    challenge_id: currentConnectionData.challenge_id
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Instagram account connected successfully with 2FA!<br>
                            Account ID: ${data.account_id}<br>
                            <small>You can now use comment replies and story interactions.</small>
                        </div>
                    `;
                    hide2FAInput();
                    // Refresh status after successful connection
                    setTimeout(checkConnectionStatus, 1000);
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ 2FA verification failed: ${data.error || data.message}<br>
                            <small>Please check your 2FA code and try again.</small>
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ 2FA verification error: ' + error.message + '</div>';
            });
        }

        // Allow Enter key to submit 2FA code
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('twoFactorCode').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    submitTwoFactor();
                }
            });
        });



        function getAccountInfo() {
            const resultDiv = document.getElementById('accountInfo');
            resultDiv.innerHTML = '<div class="info">🔄 Getting account information...</div>';

            fetch('/api/instagram/account-info')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        const account = data.data;
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h4>📊 Account Information</h4>
                                <p><strong>Username:</strong> ${account.username}</p>
                                <p><strong>Name:</strong> ${account.name}</p>
                                <p><strong>Account ID:</strong> ${account.id}</p>
                                <p><strong>Account Type:</strong> ${account.account_type}</p>
                                <p><strong>Provider:</strong> ${account.provider}</p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="warning">
                                ⚠️ ${data.message || data.error}<br>
                                <small>Please connect your Instagram account first.</small>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="error">❌ Error getting account info: ' + error.message + '</div>';
                });
        }

        function replyToComment() {
            const commentId = document.getElementById('commentId').value;
            const replyText = document.getElementById('commentReply').value;
            const resultDiv = document.getElementById('commentReplyResult');

            if (!commentId || !replyText) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter both comment ID and reply text</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">🔄 Sending comment reply...</div>';

            fetch('/api/instagram/reply-comment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    comment_id: commentId,
                    reply_text: replyText
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Comment reply sent successfully!</div>';
                    // Clear form
                    document.getElementById('commentId').value = '';
                    document.getElementById('commentReply').value = '';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send reply: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Error sending reply: ' + error.message + '</div>';
            });
        }

        function getPostComments() {
            const postId = document.getElementById('postId').value;
            const resultDiv = document.getElementById('commentsResult');

            if (!postId) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter a post ID</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">🔄 Getting post comments...</div>';

            fetch(`/api/instagram/post-comments?post_id=${postId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        let commentsHtml = '<div class="success"><h4>📝 Post Comments:</h4>';
                        if (data.data.length > 0) {
                            data.data.forEach(comment => {
                                commentsHtml += `<p><strong>${comment.username}:</strong> ${comment.text}</p>`;
                            });
                        } else {
                            commentsHtml += '<p>No comments found for this post.</p>';
                        }
                        commentsHtml += '</div>';
                        resultDiv.innerHTML = commentsHtml;
                    } else {
                        resultDiv.innerHTML = '<div class="error">❌ Failed to get comments: ' + (data.error || 'Unknown error') + '</div>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="error">❌ Error getting comments: ' + error.message + '</div>';
                });
        }

        function replyToStory() {
            const storyId = document.getElementById('storyId').value;
            const replyText = document.getElementById('storyReply').value;
            const resultDiv = document.getElementById('storyReplyResult');

            if (!storyId || !replyText) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter both story ID and reply text</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">🔄 Sending story reply...</div>';

            fetch('/api/instagram/reply-story', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    story_id: storyId,
                    message: replyText
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Story reply sent successfully!</div>';
                    // Clear form
                    document.getElementById('storyId').value = '';
                    document.getElementById('storyReply').value = '';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send story reply: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Error sending story reply: ' + error.message + '</div>';
            });
        }

        function getConversations() {
            const resultDiv = document.getElementById('conversationsResult');
            resultDiv.innerHTML = '<div class="info">🔄 Getting conversations...</div>';

            fetch('/api/instagram/conversations')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        let conversationsHtml = '<div class="success"><h4>💬 Conversations:</h4>';
                        if (data.data.length > 0) {
                            data.data.forEach(conversation => {
                                conversationsHtml += `<p><strong>${conversation.name || conversation.username}:</strong> ${conversation.last_message || 'No recent messages'}</p>`;
                            });
                        } else {
                            conversationsHtml += '<p>No conversations found.</p>';
                        }
                        conversationsHtml += '</div>';
                        resultDiv.innerHTML = conversationsHtml;
                    } else {
                        resultDiv.innerHTML = '<div class="error">❌ Failed to get conversations: ' + (data.error || 'Unknown error') + '</div>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="error">❌ Error getting conversations: ' + error.message + '</div>';
                });
        }

        function show2FAGuidance() {
            const resultDiv = document.getElementById('accountInfo');
            resultDiv.innerHTML = '<div class="info">🔄 Loading 2FA guidance...</div>';

            fetch('/api/instagram/2fa-guidance')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        const guidance = data.data;
                        let guidanceHtml = `
                            <div class="info" style="text-align: left;">
                                <h4>🔐 ${guidance.message}</h4>
                                <h5>📋 Steps to Connect:</h5>
                                <ol>`;

                        guidance.steps.forEach(step => {
                            guidanceHtml += `<li>${step}</li>`;
                        });

                        guidanceHtml += `</ol>
                                <h5>📱 Account Requirements:</h5>
                                <ul>`;

                        guidance.account_requirements.forEach(req => {
                            guidanceHtml += `<li>${req}</li>`;
                        });

                        guidanceHtml += `</ul>
                                <h5>🔧 Common Issues & Solutions:</h5>
                                <ul>
                                    <li><strong>2FA Error:</strong> ${guidance.troubleshooting['2fa_error']}</li>
                                    <li><strong>Checkpoint Error:</strong> ${guidance.troubleshooting.checkpoint_error}</li>
                                    <li><strong>Login Required:</strong> ${guidance.troubleshooting.login_required}</li>
                                    <li><strong>Challenge Required:</strong> ${guidance.troubleshooting.challenge_required}</li>
                                </ul>

                            </div>
                        `;
                        resultDiv.innerHTML = guidanceHtml;
                    } else {
                        resultDiv.innerHTML = '<div class="error">❌ Failed to load 2FA guidance: ' + (data.error || 'Unknown error') + '</div>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="error">❌ Error loading 2FA guidance: ' + error.message + '</div>';
                });
        }
    </script>
</body>
</html>
