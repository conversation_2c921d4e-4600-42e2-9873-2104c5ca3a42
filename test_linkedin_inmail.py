#!/usr/bin/env python3
"""
Test script to debug LinkedIn InMail sending issues
"""

import requests
import json

def test_linkedin_inmail():
    """Test LinkedIn InMail sending with debug information"""
    
    print("🧪 LinkedIn InMail Debug Test")
    print("=" * 60)
    
    # Test data
    test_data = {
        "recipient_id": "john-doe",  # Replace with a real LinkedIn profile identifier
        "subject": "Test InMail Subject",
        "message_body": "Hello! This is a test InMail message to verify the functionality is working correctly."
    }
    
    print(f"📧 Test InMail Data:")
    print(f"   Recipient: {test_data['recipient_id']}")
    print(f"   Subject: {test_data['subject']}")
    print(f"   Message Length: {len(test_data['message_body'])} characters")
    print()
    
    # Test the debug endpoint
    debug_url = "http://localhost:8000/api/linkedin/debug-inmail"
    
    try:
        print("🔍 Testing Debug InMail Endpoint...")
        response = requests.post(debug_url, json=test_data, timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Debug endpoint successful!")
            print(f"📋 Debug Info:")
            debug_info = data.get("debug_info", {})
            for key, value in debug_info.items():
                print(f"   {key}: {value}")
            
            print(f"📧 InMail Result:")
            result = data.get("result", {})
            for key, value in result.items():
                print(f"   {key}: {value}")
            
            if data.get("success"):
                print("🎉 InMail sent successfully!")
            else:
                print("❌ InMail failed to send")
                error = result.get("error", "Unknown error")
                print(f"   Error: {error}")
                
        else:
            print(f"❌ Debug endpoint failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running?")
        print("   Start server with: python integrations/api_endpoints.py")
    except requests.exceptions.Timeout:
        print("❌ Request timeout")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_regular_inmail():
    """Test the regular InMail endpoint"""
    
    print("\n🔄 Testing Regular InMail Endpoint")
    print("=" * 60)
    
    # Test data
    test_data = {
        "recipient_id": "john-doe",  # Replace with a real LinkedIn profile identifier
        "subject": "Regular Test InMail",
        "message_body": "This is a test of the regular InMail endpoint."
    }
    
    regular_url = "http://localhost:8000/api/linkedin/send-inmail"
    
    try:
        print("📧 Testing Regular InMail Endpoint...")
        response = requests.post(regular_url, json=test_data, timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Regular InMail endpoint successful!")
            print(f"📧 Result: {data}")
        else:
            print(f"❌ Regular InMail endpoint failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error testing regular endpoint: {e}")

def test_linkedin_status():
    """Test LinkedIn connection status"""
    
    print("\n📊 Testing LinkedIn Status")
    print("=" * 60)
    
    status_url = "http://localhost:8000/api/linkedin/unipile/status"
    
    try:
        response = requests.get(status_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ LinkedIn status retrieved successfully!")
            print(f"📊 Status: {data}")
        else:
            print(f"❌ Status check failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error checking status: {e}")

def test_chat_history():
    """Test the new chat history functionality"""
    
    print("\n💬 Testing Chat History Features")
    print("=" * 60)
    
    endpoints = [
        ("conversations", "http://localhost:8000/api/linkedin/conversations?limit=5"),
        ("inmail-history", "http://localhost:8000/api/linkedin/inmail-history?limit=5")
    ]
    
    for name, url in endpoints:
        try:
            print(f"🔍 Testing {name}...")
            response = requests.get(url, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {name} endpoint working!")
                if data.get("success"):
                    count = len(data.get("data", []))
                    print(f"   Found {count} items")
                else:
                    print(f"   Response: {data}")
            else:
                print(f"❌ {name} failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error testing {name}: {e}")

if __name__ == "__main__":
    print("🚀 LinkedIn InMail & Chat History Test Suite")
    print("=" * 60)
    print("This script will test the LinkedIn InMail functionality and chat history features.")
    print()
    
    # Test LinkedIn status first
    test_linkedin_status()
    
    # Test chat history features
    test_chat_history()
    
    # Test InMail functionality
    test_linkedin_inmail()
    test_regular_inmail()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print("1. ✅ Check if LinkedIn account is connected")
    print("2. ✅ Test new chat history endpoints")
    print("3. 🧪 Debug InMail sending with detailed logging")
    print("4. 📧 Test regular InMail endpoint")
    print()
    print("💡 Tips:")
    print("- Make sure the server is running: python integrations/api_endpoints.py")
    print("- Replace 'john-doe' with a real LinkedIn profile identifier")
    print("- Check server logs for detailed debug information")
    print("- The debug endpoint shows exactly what's happening with the API calls")
    print()
    print("🔧 If InMail fails:")
    print("- Check the debug output for specific error messages")
    print("- Verify the recipient_id format (should be LinkedIn profile identifier)")
    print("- Ensure your LinkedIn account has InMail privileges")
    print("- Check Unipile API documentation for InMail requirements")
