"""
Unipile Configuration and Client Setup
Centralized configuration for Unipile API integration
"""

import os
import json
import logging
from typing import Dict, Optional
from dataclasses import dataclass

# Import our custom UnipileAPI implementation
from unipile_api import UnipileAPI as UnipileClient

@dataclass
class UnipileConfig:
    """Unipile API configuration"""
    api_key: str = ""
    base_url: str = "https://api14.unipile.com:14455"
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    
    def __init__(self, config_path: str = "integrations/unipile_config.json"):
        """Initialize configuration from file or environment variables"""
        self.config_path = config_path
        self._load_config()
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self):
        """Load configuration from JSON file or environment variables"""
        # Try to load from file first
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    config_data = json.load(f)
                    self.api_key = config_data.get("api_key", "")
                    self.base_url = config_data.get("base_url", self.base_url)
                    self.timeout = config_data.get("timeout", self.timeout)
                    self.max_retries = config_data.get("max_retries", self.max_retries)
                    self.retry_delay = config_data.get("retry_delay", self.retry_delay)
            except (json.JSONDecodeError, FileNotFoundError) as e:
                logging.warning(f"Could not load config file: {e}")
        
        # Override with environment variables if available
        self.api_key = os.getenv("UNIPILE_API_KEY", self.api_key)
        self.base_url = os.getenv("UNIPILE_BASE_URL", self.base_url)
    
    def save_config(self):
        """Save current configuration to file"""
        config_data = {
            "api_key": self.api_key,
            "base_url": self.base_url,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay
        }
        
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(config_data, f, indent=2)
            self.logger.info(f"Configuration saved to {self.config_path}")
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
    
    def get_client(self) -> UnipileClient:
        """Get configured Unipile client instance"""
        if not self.api_key:
            raise ValueError("Unipile API key not configured. Please set UNIPILE_API_KEY environment variable or update config file.")

        return UnipileClient(
            api_key=self.api_key,
            base_url=self.base_url
        )
    
    def is_configured(self) -> bool:
        """Check if Unipile is properly configured"""
        return bool(self.api_key)
    
    def test_connection(self) -> Dict:
        """Test Unipile API connection"""
        if not self.is_configured():
            return {"error": "Unipile API key not configured"}

        try:
            client = self.get_client()
            # Test connection by getting accounts
            response = client.get_accounts()
            if "error" in response:
                return {"error": f"Unipile connection failed: {response['error']}"}
            return {
                "success": True,
                "message": "Unipile connection successful",
                "accounts_count": len(response.get("items", []))
            }
        except Exception as e:
            return {
                "error": f"Unipile connection failed: {str(e)}"
            }

# Global configuration instance
unipile_config = UnipileConfig()

def get_unipile_client() -> UnipileClient:
    """Get global Unipile client instance"""
    return unipile_config.get_client()

def is_unipile_available() -> bool:
    """Check if Unipile is available and configured"""
    return unipile_config.is_configured()

# Example usage
if __name__ == "__main__":
    config = UnipileConfig()
    
    if config.is_configured():
        print("✅ Unipile is configured")
        
        # Test connection
        test_result = config.test_connection()
        if test_result.get("success"):
            print(f"✅ Connection successful: {test_result['message']}")
            print(f"📊 Connected accounts: {test_result['accounts_count']}")
        else:
            print(f"❌ Connection failed: {test_result['error']}")
    else:
        print("❌ Unipile not configured")
        print("Please set your API key in environment variable UNIPILE_API_KEY or config file")
