"""
Instagram Messaging API Integration
Handles authentication and messaging for Instagram Business/Creator accounts
Uses Unipile API for unified Instagram messaging
Focus on comment replies and story interactions due to Instagram's messaging limitations
"""

import json
import time
from typing import Dict, List, Optional
import logging
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from unipile_api import UnipileAPI

class InstagramMessaging:
    def __init__(self, config_path: str = None):
        """Initialize Instagram API client with Unipile"""
        # Determine config path based on current working directory
        if config_path is None:
            import os
            current_dir = os.getcwd()
            if current_dir.endswith('integrations'):
                # Running from integrations directory
                config_path = "instagram_integration/config.json"
            else:
                # Running from root directory
                config_path = "integrations/instagram_integration/config.json"

        self.config_path = config_path

        # Setup logging first
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Load config after logger is initialized
        self.config = self._load_config()

        # Initialize Unipile client
        try:
            # Get API key from config or use default
            unipile_config = self.config.get("unipile", {})
            api_key = unipile_config.get("api_key", "0wYBrLyQ.2oA9eegTBThrd+sGgO1scm8q25M9pqIVY5Xn2VfHs9M=")
            base_url = unipile_config.get("api_url", "https://api14.unipile.com:14455")

            if not api_key:
                api_key = "0wYBrLyQ.2oA9eegTBThrd+sGgO1scm8q25M9pqIVY5Xn2VfHs9M="
                self.logger.warning("No API key found in config, using default")

            self.unipile_client = UnipileAPI(api_key=api_key, base_url=base_url)
            self.logger.info(f"✅ Unipile client initialized with API key: {api_key[:20]}...")

        except Exception as e:
            self.logger.error(f"Failed to initialize Unipile client: {e}")
            self.unipile_client = None

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.2  # 200ms between requests

    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.logger.info(f"✅ Config loaded from {self.config_path}")
                return config
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {self.config_path}, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in config file: {e}")
            return self._get_default_config()
        except UnicodeDecodeError as e:
            self.logger.error(f"Unicode decode error in config file: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict:
        """Get default configuration"""
        return {
            "unipile": {
                "api_url": "https://api14.unipile.com:14455",
                "api_key": "0wYBrLyQ.2oA9eegTBThrd+sGgO1scm8q25M9pqIVY5Xn2VfHs9M=",
                "account_id": "",
                "enabled": True
            },
            "rate_limit": {
                "messages_per_second": 2,
                "messages_per_minute": 60,
                "messages_per_hour": 500
            },
            "settings": {
                "auto_retry": True,
                "max_retries": 3,
                "retry_delay": 3,
                "log_level": "INFO"
            }
        }

    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()

    def connect_account(self, username: str, password: str) -> Dict:
        """Connect Instagram account using credentials via Unipile"""
        if not self.unipile_client:
            return {"success": False, "error": "Unipile client not available"}

        try:
            connect_data = {
                "provider": "INSTAGRAM",
                "username": username,
                "password": password
            }

            self.logger.info(f"Attempting to connect Instagram account: {username}")
            result = self.unipile_client._make_request("POST", "api/v1/accounts", connect_data)

            if "error" not in result:
                account_id = result.get("account_id") or result.get("id")
                self.logger.info(f"Instagram account connected successfully: {account_id}")

                return {
                    "success": True,
                    "account_id": account_id,
                    "message": "Instagram account connected successfully",
                    "account_info": result
                }
            else:
                error_msg = result.get("error", "Unknown error")
                self.logger.error(f"Failed to connect Instagram account: {error_msg}")

                # Enhanced error handling for common Instagram issues
                error_msg_str = str(error_msg).lower() if error_msg else ""
                if "2fa" in error_msg_str or "two-factor" in error_msg_str:
                    enhanced_msg = "Instagram authentication requires 2FA verification. Please ensure your account credentials are correct and try again."
                elif "checkpoint" in error_msg_str:
                    enhanced_msg = "Instagram security checkpoint detected. Please verify your account through Instagram's official app and try again."
                elif "login_required" in error_msg_str:
                    enhanced_msg = "Instagram login verification required. Please check your credentials and ensure your account is accessible."
                elif "challenge_required" in error_msg_str:
                    enhanced_msg = "Instagram challenge verification required. Please complete any pending security challenges in the Instagram app."
                else:
                    enhanced_msg = f"Instagram connection failed: {error_msg}. Please ensure you're using a Business or Creator account with correct credentials."

                return {
                    "success": False,
                    "error": error_msg,
                    "message": enhanced_msg,
                    "requires_2fa": "2fa" in error_msg_str or "two-factor" in error_msg_str or "checkpoint" in error_msg_str
                }

        except Exception as e:
            self.logger.error(f"Error connecting Instagram account: {e}")
            error_str = str(e)

            # Check for 2FA-related errors in exception message
            if isinstance(e, str) and ("2FA" in error_str or "two-factor" in error_str.lower() or "checkpoint" in error_str.lower()):
                return {
                    "success": False,
                    "error": "Instagram authentication requires 2FA verification",
                    "message": "Please ensure your account credentials are correct and complete any required verification steps."
                }

            return {
                "success": False,
                "error": error_str,
                "message": "An error occurred while connecting your Instagram account.",
                "requires_2fa": "2FA" in error_str or "two-factor" in error_str.lower() or "checkpoint" in error_str.lower()
            }

    def verify_2fa(self, username: str, password: str, two_factor_code: str, challenge_id: str = None) -> Dict:
        """Verify 2FA code and complete Instagram account connection"""
        if not self.unipile_client:
            return {"success": False, "error": "Unipile client not available"}

        try:
            connect_data = {
                "provider": "INSTAGRAM",
                "username": username,
                "password": password,
                "two_factor_code": two_factor_code
            }

            # Add challenge_id if provided
            if challenge_id:
                connect_data["challenge_id"] = challenge_id

            self.logger.info(f"Attempting to verify 2FA for Instagram account: {username}")
            result = self.unipile_client._make_request("POST", "api/v1/accounts", connect_data)

            if "error" not in result:
                account_id = result.get("account_id") or result.get("id")
                self.logger.info(f"Instagram account connected successfully with 2FA: {account_id}")

                return {
                    "success": True,
                    "account_id": account_id,
                    "message": "Instagram account connected successfully with 2FA verification",
                    "account_info": result
                }
            else:
                error_msg = result.get("error", "Unknown error")
                self.logger.error(f"Failed to verify 2FA for Instagram account: {error_msg}")

                return {
                    "success": False,
                    "error": error_msg,
                    "message": f"2FA verification failed: {error_msg}. Please check your code and try again."
                }

        except Exception as e:
            self.logger.error(f"Error verifying 2FA for Instagram account: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "An error occurred while verifying your 2FA code."
            }

    def get_account_info(self) -> Dict:
        """Get Instagram account information"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            accounts = self.unipile_client.get_accounts()
            
            if "error" in accounts:
                return {"error": accounts["error"]}
            
            # Find Instagram accounts
            instagram_accounts = []
            for account in accounts.get("items", []):
                if account.get("type", "").lower() == "instagram":
                    instagram_accounts.append(account)
            
            if not instagram_accounts:
                return {"error": "No Instagram accounts found"}
            
            # Return the first Instagram account
            account = instagram_accounts[0]
            return {
                "success": True,
                "username": account.get("username", "N/A"),
                "name": account.get("name", "N/A"),
                "id": account.get("id", "N/A"),
                "account_type": account.get("account_type", "N/A"),
                "provider": "instagram"
            }

        except Exception as e:
            self.logger.error(f"Error getting account info: {e}")
            return {"error": str(e)}

    def get_connection_status(self) -> Dict:
        """Get Instagram connection status"""
        if not self.unipile_client:
            return {
                "unipile": {
                    "available": False,
                    "connected": False,
                    "accounts": [],
                    "error": "Unipile client not available"
                }
            }

        try:
            accounts = self.unipile_client.get_accounts()
            
            if "error" in accounts:
                return {
                    "unipile": {
                        "available": True,
                        "connected": False,
                        "accounts": [],
                        "error": accounts["error"]
                    }
                }
            
            # Find Instagram accounts
            instagram_accounts = []
            for account in accounts.get("items", []):
                if account.get("type", "").lower() == "instagram":
                    instagram_accounts.append({
                        "id": account.get("id"),
                        "username": account.get("username"),
                        "name": account.get("name")
                    })
            
            return {
                "unipile": {
                    "available": True,
                    "connected": len(instagram_accounts) > 0,
                    "accounts": instagram_accounts
                }
            }

        except Exception as e:
            self.logger.error(f"Error getting connection status: {e}")
            return {
                "unipile": {
                    "available": True,
                    "connected": False,
                    "accounts": [],
                    "error": str(e)
                }
            }

    def reply_to_comment(self, comment_id: str, reply_text: str) -> Dict:
        """Reply to an Instagram comment"""
        if not self.unipile_client:
            return {"success": False, "error": "Unipile client not available"}

        self._rate_limit()

        try:
            result = self.unipile_client.reply_to_instagram_comment(comment_id, reply_text)
            
            if "error" not in result:
                self.logger.info(f"Comment reply sent successfully to comment {comment_id}")
                return {"success": True, "result": result}
            else:
                self.logger.error(f"Failed to reply to comment: {result}")
                return {"success": False, "error": result.get('error')}
                
        except Exception as e:
            self.logger.error(f"Comment reply error: {e}")
            return {"success": False, "error": str(e)}

    def get_post_comments(self, post_id: str, limit: int = 25) -> Dict:
        """Get comments from an Instagram post"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            result = self.unipile_client.get_instagram_post_comments(post_id, limit)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"Error getting post comments: {e}")
            return {"error": str(e)}

    def send_story_reply(self, story_id: str, message: str) -> Dict:
        """Reply to an Instagram story"""
        if not self.unipile_client:
            return {"success": False, "error": "Unipile client not available"}

        self._rate_limit()

        try:
            result = self.unipile_client.reply_to_instagram_story(story_id, message)
            
            if "error" not in result:
                self.logger.info(f"Story reply sent successfully to story {story_id}")
                return {"success": True, "result": result}
            else:
                self.logger.error(f"Failed to reply to story: {result}")
                return {"success": False, "error": result.get('error')}
                
        except Exception as e:
            self.logger.error(f"Story reply error: {e}")
            return {"success": False, "error": str(e)}

    def get_conversations(self, limit: int = 25) -> Dict:
        """Get Instagram conversations (limited due to Instagram restrictions)"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            result = self.unipile_client.get_conversations("instagram", limit=limit)
            return {"success": True, "data": result}
        except Exception as e:
            self.logger.error(f"Error getting conversations: {e}")
            return {"error": str(e)}

    def is_configured(self) -> bool:
        """Check if Instagram API is properly configured"""
        return self.unipile_client is not None

    def get_2fa_guidance(self) -> Dict:
        """Get guidance for handling 2FA authentication"""
        return {
            "message": "Instagram Authentication Guide",
            "steps": [
                "Ensure you're using a Business or Creator account for best compatibility",
                "Verify your Instagram credentials are correct",
                "Complete any pending security verifications in the Instagram app",
                "If 2FA is enabled, ensure you have access to your authentication device",
                "Try connecting during off-peak hours for better success rates"
            ],
            "account_requirements": [
                "Instagram Business Account (recommended)",
                "Instagram Creator Account (alternative)",
                "Valid and verified credentials",
                "Access to your 2FA device if enabled (phone/authenticator app)"
            ],
            "troubleshooting": {
                "2fa_error": "Complete 2FA verification in Instagram app, then try connecting again",
                "checkpoint_error": "Complete security verification in Instagram's official app",
                "login_required": "Verify account credentials and complete any pending verifications",
                "challenge_required": "Complete security challenge in Instagram app before connecting"
            }
        }

# Alias for backward compatibility
InstagramAPI = InstagramMessaging

# Example usage
if __name__ == "__main__":
    # Initialize Instagram messaging with Unipile
    instagram = InstagramMessaging()

    # Check connection status
    status = instagram.get_connection_status()
    print(f"Connection status: {status}")

    # Check if configured
    if not instagram.is_configured():
        print("Instagram API not configured. Please ensure Unipile API is available.")
    else:
        # Example: Get account info
        account_info = instagram.get_account_info()
        print(f"Account info: {account_info}")

        # Example: Reply to comment (replace with actual comment ID)
        # reply_result = instagram.reply_to_comment("COMMENT_ID", "Thanks for your comment! 😊")
        # print(f"Comment reply result: {reply_result}")
