<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn Professional Messaging</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #0077b5 0%, #005885 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #0077b5;
            font-size: 2.2em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
            margin: 0;
        }
        
        .section {
            background: #f8f9fa;
            border-left: 4px solid #0077b5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .section h3 {
            color: #0077b5;
            margin-top: 0;
            font-size: 1.3em;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #0077b5;
        }
        
        .button {
            background: #0077b5;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: background 0.3s;
        }
        
        .button:hover {
            background: #005885;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 12px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .info {
            background: #cce5ff;
            border: 1px solid #99ccff;
            color: #004085;
            padding: 12px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .messaging-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .messaging-card h4 {
            color: #0077b5;
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .messaging-card p {
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💼 LinkedIn Professional Messaging</h1>
            <p>Connection requests, InMail, and company page messaging</p>
        </div>

        <!-- Connection Setup -->
        <div class="section">
            <h3>🔗 Step 1: Connect Your LinkedIn Account</h3>
            <div class="info">
                <strong>Connect via Unipile to enable professional messaging features</strong>
            </div>

            <div class="form-group">
                <label for="unipileApiKey">Unipile API Key:</label>
                <input type="password" id="unipileApiKey" name="unipileApiKey"
                       value="iGJsHDIR.sr6Pg30nB1cKUPC6eDY8tzVx+Opu+t+c6wQiKAEz2Ek="
                       placeholder="Your Unipile API key">
            </div>

            <button type="button" onclick="connectUnipile()">Connect API Key</button>
            <button type="button" onclick="checkUnipileStatus()">Check Connection Status</button>

            <div id="unipileResult"></div>
            <div id="connectionStatus"></div>

            <!-- LinkedIn Account Connection -->
            <div class="form-group" style="margin-top: 20px;">
                <h4>🔐 Add LinkedIn Account</h4>
                <div class="info">
                    <strong>Connect your LinkedIn account using your credentials:</strong>
                </div>

                <!-- Direct Credentials Connection -->
                <div style="margin: 15px 0; padding: 15px; border: 2px solid #0077b5; border-radius: 8px;">
                    <h5>🔑 LinkedIn Account Connection</h5>
                    <p>Connect using your LinkedIn username and password. Your account will automatically appear in your Unipile dashboard.</p>
                    <div class="form-group">
                        <label for="linkedinUsername">LinkedIn Username/Email:</label>
                        <input type="text" id="linkedinUsername" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="linkedinPassword">LinkedIn Password:</label>
                        <input type="password" id="linkedinPassword" placeholder="Your LinkedIn password">
                    </div>
                    <button type="button" onclick="connectLinkedInDirect()" style="background: #0077b5;">
                        🔐 Connect LinkedIn Account
                    </button>
                    <div id="directConnectResult"></div>

                    <!-- Checkpoint Verification (hidden by default) -->
                    <div id="checkpointSection" style="display: none; margin-top: 15px; padding: 15px; border: 2px solid #ffc107; border-radius: 8px; background: #fff3cd;">
                        <h5>🔐 Verification Required</h5>
                        <p id="checkpointMessage">LinkedIn requires additional verification to complete the connection.</p>

                        <!-- CAPTCHA Section (for CAPTCHA checkpoints) -->
                        <div id="captchaSection" style="display: none; margin: 15px 0;">
                            <div id="captchaContainer" style="margin: 10px 0; text-align: center;">
                                <!-- CAPTCHA will be loaded here -->
                            </div>
                            <div class="form-group">
                                <label for="captchaResponse">CAPTCHA Response:</label>
                                <input type="text" id="captchaResponse" placeholder="Complete the CAPTCHA above">
                                <small>Complete the CAPTCHA verification above, then click Verify.</small>
                            </div>
                        </div>

                        <!-- Regular Code Section (for 2FA, OTP, etc.) -->
                        <div id="codeSection">
                            <div class="form-group">
                                <label for="verificationCode">Verification Code:</label>
                                <input type="text" id="verificationCode" placeholder="Enter verification code">
                                <small id="checkpointInstructions">Check your email, SMS, or LinkedIn mobile app for the verification code.</small>
                            </div>
                        </div>

                        <button type="button" onclick="solveCheckpoint()" style="background: #ffc107; color: #212529;">
                            ✅ Verify Code
                        </button>
                        <button type="button" onclick="refreshCaptcha()" id="refreshCaptchaBtn" style="display: none; background: #6c757d; color: white; margin-left: 10px;">
                            🔄 Refresh CAPTCHA
                        </button>
                        <div id="checkpointResult"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat History Section -->
        <div class="section">
            <h3>💬 Chat History & InMail Tracking</h3>
            <div class="info">
                <strong>View your LinkedIn conversations, sent InMails, and received responses</strong>
            </div>

            <div class="form-group">
                <button type="button" onclick="loadConversations()" style="background: #0077b5;">
                    📋 Load All Conversations
                </button>
                <button type="button" onclick="loadInMailHistory()" style="background: #0077b5;">
                    📧 Load InMail History
                </button>
                <button type="button" onclick="showSearchInterface()" style="background: #0077b5;">
                    🔍 Search Chat History
                </button>
            </div>

            <!-- Search Interface -->
            <div id="searchInterface" style="display: none; margin: 15px 0; padding: 15px; border: 2px solid #0077b5; border-radius: 8px;">
                <h5>🔍 Search Chat History</h5>
                <div class="form-group">
                    <label for="searchQuery">Search Query:</label>
                    <input type="text" id="searchQuery" placeholder="Enter keywords to search for...">
                </div>
                <button type="button" onclick="searchChatHistory()" style="background: #0077b5;">
                    🔍 Search Messages
                </button>
                <div id="searchResults"></div>
            </div>

            <!-- Chat History Display -->
            <div id="chatHistoryContainer" style="margin-top: 20px;">
                <div id="chatHistoryResult"></div>

                <!-- Conversations List -->
                <div id="conversationsList" style="display: none;">
                    <h4>💬 Your LinkedIn Conversations</h4>
                    <div id="conversationsContainer" style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; border-radius: 8px; padding: 10px;">
                        <!-- Conversations will be loaded here -->
                    </div>
                </div>

                <!-- Chat Messages Display -->
                <div id="chatMessagesContainer" style="display: none; margin-top: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <h4 id="chatTitle">💬 Chat Messages</h4>
                        <button type="button" onclick="closeChatView()" style="background: #666; padding: 5px 10px;">✕ Close</button>
                    </div>
                    <div id="messagesContainer" style="max-height: 500px; overflow-y: auto; border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: #f8f9fa;">
                        <!-- Messages will be loaded here -->
                    </div>
                </div>

                <!-- InMail History Display -->
                <div id="inmailHistoryContainer" style="display: none;">
                    <h4>📧 InMail History</h4>
                    <div id="inmailContainer" style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; border-radius: 8px; padding: 10px;">
                        <!-- InMail history will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Professional Messaging Features -->
        <div class="section">
            <h3>💬 Step 2: Professional Messaging Features</h3>

            <!-- Helper: How to find LinkedIn Person IDs -->
            <div style="margin: 15px 0; padding: 15px; background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 8px;">
                <h5>💡 How to Find LinkedIn Person IDs</h5>
                <p><strong>Method 1: Public Identifier (Easiest)</strong></p>
                <ul>
                    <li>Go to someone's LinkedIn profile</li>
                    <li>Copy the identifier from the URL: <code>linkedin.com/in/<strong>john-doe</strong></code></li>
                    <li>Use just the identifier part: <code>john-doe</code></li>
                </ul>
                <p><strong>Method 2: Provider ID (Advanced)</strong></p>
                <ul>
                    <li>Use LinkedIn provider ID format: <code>ACoAAABCDEF...</code></li>
                    <li>This is obtained from LinkedIn API responses</li>
                </ul>
            </div>

            <!-- Connection Requests -->
            <div class="messaging-card">
                <h4>🤝 Connection Requests with Messages</h4>
                <p>Send personalized connection requests to expand your professional network</p>

                <div class="info" style="margin-bottom: 15px;">
                    <strong>📋 Requirements:</strong><br>
                    1. Connect your LinkedIn account above first<br>
                    2. Use LinkedIn Person ID (e.g., ACoAAABCDEF) or public identifier (e.g., john-doe)<br>
                    3. Account must appear in "Connection Status" before sending messages
                </div>

                <div class="form-group">
                    <label for="connectionPersonId">LinkedIn Person ID or Public Identifier:</label>
                    <input type="text" id="connectionPersonId" placeholder="e.g., ACoAAABCDEF or john-doe">
                    <small>You can use either the LinkedIn provider ID or the public profile identifier (from LinkedIn URL)</small>
                </div>

                <div class="form-group">
                    <label for="connectionMessage">Connection Message:</label>
                    <textarea id="connectionMessage" rows="3" placeholder="I'd like to connect with you to expand my professional network."></textarea>
                </div>

                <button type="button" onclick="sendConnectionRequest()">Send Connection Request</button>
                <div id="connectionResult"></div>
            </div>

            <!-- Profile Search -->
            <div class="messaging-card">
                <h4>🔍 Find LinkedIn Profiles</h4>
                <p>Search for LinkedIn profiles to find valid recipient IDs</p>

                <div class="form-group">
                    <label for="profileSearchQuery">Search for profiles:</label>
                    <input type="text" id="profileSearchQuery" placeholder="e.g., John Doe, Software Engineer, Microsoft">
                </div>

                <button type="button" onclick="searchProfiles()">🔍 Search Profiles</button>
                <div id="profileSearchResults"></div>
            </div>

            <!-- InMail -->
            <div class="messaging-card">
                <h4>📧 InMail Messaging</h4>
                <p>Send professional messages to LinkedIn members outside your network</p>

                <div class="form-group">
                    <label for="inmailRecipientId">Recipient LinkedIn ID or Name:</label>
                    <input type="text" id="inmailRecipientId" placeholder="e.g., ACoAAABCDEF, john-doe, or John Doe">
                    <small style="color: #666; display: block; margin-top: 5px;">💡 Tip: Use Smart InMail for names or partial matches</small>
                </div>
                
                <div class="form-group">
                    <label for="inmailSubject">Subject:</label>
                    <input type="text" id="inmailSubject" placeholder="Professional Opportunity">
                </div>
                
                <div class="form-group">
                    <label for="inmailMessage">Message:</label>
                    <textarea id="inmailMessage" rows="4" placeholder="Hello, I hope this message finds you well..."></textarea>
                </div>
                
                <button type="button" onclick="sendInMail()">Send InMail</button>
                <button type="button" onclick="sendSmartInMail()" style="background: #4caf50; margin-left: 10px;">🧠 Smart InMail</button>
                <button type="button" onclick="debugInMail()" style="background: #ff9800; margin-left: 10px;">🔍 Debug InMail</button>
                <div id="inmailResult"></div>
            </div>

            <!-- Company Page Messaging -->
            <div class="messaging-card">
                <h4>🏢 Company Page Messaging</h4>
                <p>Send messages from your company page to prospects and customers</p>
                
                <div class="form-group">
                    <label for="companyId">Company ID:</label>
                    <input type="text" id="companyId" placeholder="e.g., 123456">
                </div>
                
                <div class="form-group">
                    <label for="companyRecipientId">Recipient LinkedIn ID:</label>
                    <input type="text" id="companyRecipientId" placeholder="e.g., ACoAAABCDEF">
                </div>
                
                <div class="form-group">
                    <label for="companyMessage">Company Message:</label>
                    <textarea id="companyMessage" rows="4" placeholder="Thank you for your interest in our company..."></textarea>
                </div>
                
                <button type="button" onclick="sendCompanyMessage()">Send Company Message</button>
                <div id="companyResult"></div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="section">
            <h3>📋 How to Get LinkedIn IDs</h3>
            <div class="info">
                <strong>To find LinkedIn Person/Company IDs:</strong><br>
                1. Visit the LinkedIn profile or company page<br>
                2. Look at the URL: linkedin.com/in/<strong>person-id</strong> or linkedin.com/company/<strong>company-id</strong><br>
                3. Use LinkedIn Sales Navigator or LinkedIn API to get internal IDs<br>
                4. For Unipile integration, accounts will show their IDs in the connection status
            </div>
        </div>
    </div>

    <script>
        function connectUnipile() {
            const apiKey = document.getElementById('unipileApiKey').value;
            if (!apiKey) {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Please enter your Unipile API key</div>';
                return;
            }

            document.getElementById('unipileResult').innerHTML = '<div class="info">🔄 Connecting to Unipile API...</div>';

            fetch('/api/linkedin/unipile/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    api_key: apiKey
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('unipileResult');
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Unipile API connected successfully!<br>
                            <small>Total accounts: ${data.accounts_found || 0}, LinkedIn accounts: ${data.linkedin_accounts_found || 0}</small>
                        </div>
                    `;
                    checkUnipileStatus();
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to connect: ' + (data.detail || data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function checkUnipileStatus() {
            document.getElementById('connectionStatus').innerHTML = '<div class="info">🔄 Checking connection status...</div>';

            fetch('/api/linkedin/unipile/status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('connectionStatus');
                if (data.success) {
                    const accounts = data.accounts || [];

                    if (accounts.length > 0) {
                        let html = '<div class="success"><strong>✅ Connected LinkedIn Accounts:</strong><br>';
                        accounts.forEach(account => {
                            html += `💼 Account ID: ${account.id}<br>`;
                            html += `👤 Name: ${account.name || 'N/A'}<br>`;
                            html += `📧 Email: ${account.email || 'N/A'}<br>`;
                            html += `🔗 Provider: ${account.provider || account.type || 'N/A'}<br><br>`;
                        });
                        html += '</div>';
                        statusDiv.innerHTML = html;
                    } else {
                        let debugInfo = '';
                        if (data.debug_info) {
                            debugInfo = `<br><small>Debug: API Key configured: ${data.debug_info.api_key_configured}, Response format: ${data.debug_info.response_format}</small>`;
                        }
                        statusDiv.innerHTML = `
                            <div class="info">
                                <strong>📋 Unipile API Connected</strong><br>
                                Total accounts: ${data.total_accounts || 0}, LinkedIn accounts: ${data.linkedin_accounts || 0}<br>
                                No LinkedIn accounts found. Use the method above to add a LinkedIn account,
                                then click "Check Connection Status" to refresh.${debugInfo}
                            </div>
                        `;
                    }
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ Failed to get status: ' + (data.detail || data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionStatus').innerHTML =
                    '<div class="error">❌ Status check error: ' + error.message + '</div>';
            });
        }



        function connectLinkedInDirect() {
            const username = document.getElementById('linkedinUsername').value;
            const password = document.getElementById('linkedinPassword').value;
            const resultDiv = document.getElementById('directConnectResult');

            if (!username || !password) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter both username and password</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">🔄 Connecting LinkedIn account...</div>';

            fetch('/api/linkedin/connect-account', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let debugInfo = '';
                    if (data.debug_info) {
                        debugInfo = `<br><small>Name: ${data.debug_info.account_name || 'N/A'}, Email: ${data.debug_info.account_email || 'N/A'}</small>`;
                    }
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ LinkedIn account connected successfully!<br>
                            Account ID: ${data.account_id || 'N/A'}${debugInfo}<br>
                            <small>This account will now appear in your Unipile dashboard.</small>
                        </div>
                    `;
                    // Clear the password field for security
                    document.getElementById('linkedinPassword').value = '';
                    // Refresh status
                    setTimeout(checkUnipileStatus, 2000);
                } else if (data.checkpoint_required) {
                    // Handle checkpoint requirement
                    checkpointAccountId = data.account_id;
                    showCheckpointSection(data.checkpoint_type, data.message, data);
                    resultDiv.innerHTML = `
                        <div class="info">
                            🔐 Verification Required<br>
                            ${data.message}<br>
                            <small>Please complete the verification below.</small>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to connect: ' + (data.detail || data.error || data.message || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Connection error: ' + error.message + '</div>';
            });
        }

        // Global variables to store checkpoint data
        let checkpointAccountId = null;
        let checkpointData = null;

        function showCheckpointSection(checkpointType, message, additionalData = null) {
            const checkpointSection = document.getElementById('checkpointSection');
            const checkpointMessage = document.getElementById('checkpointMessage');
            const checkpointInstructions = document.getElementById('checkpointInstructions');
            const captchaSection = document.getElementById('captchaSection');
            const codeSection = document.getElementById('codeSection');
            const refreshCaptchaBtn = document.getElementById('refreshCaptchaBtn');

            checkpointMessage.textContent = message;
            checkpointData = additionalData;

            // Show/hide sections based on checkpoint type
            if (checkpointType === 'CAPTCHA') {
                captchaSection.style.display = 'block';
                codeSection.style.display = 'none';
                refreshCaptchaBtn.style.display = 'inline-block';

                // Load CAPTCHA if data is available
                if (additionalData && additionalData.checkpoint) {
                    loadCaptcha(additionalData.checkpoint);
                }
            } else {
                captchaSection.style.display = 'none';
                codeSection.style.display = 'block';
                refreshCaptchaBtn.style.display = 'none';
            }

            // Update instructions based on checkpoint type
            let instructions = '';
            switch(checkpointType) {
                case '2FA':
                    instructions = 'Enter the 6-digit code from your authenticator app.';
                    break;
                case 'OTP':
                    instructions = 'Check your email or SMS for the verification code.';
                    break;
                case 'IN_APP_VALIDATION':
                    instructions = 'Open your LinkedIn mobile app and approve the connection request.';
                    break;
                case 'PHONE_REGISTER':
                    instructions = 'Enter your phone number with country code, e.g., (+1)5551234567';
                    break;
                case 'CAPTCHA':
                    instructions = 'Complete the CAPTCHA verification above, then click Verify.';
                    break;
                default:
                    instructions = 'Follow the verification instructions from LinkedIn.';
            }

            checkpointInstructions.textContent = instructions;
            checkpointSection.style.display = 'block';
        }

        function loadCaptcha(checkpointInfo) {
            const captchaContainer = document.getElementById('captchaContainer');

            if (checkpointInfo.type === 'CAPTCHA' && checkpointInfo.public_key) {
                // For reCAPTCHA or similar
                captchaContainer.innerHTML = `
                    <div style="border: 2px solid #ddd; padding: 20px; border-radius: 8px; background: #f8f9fa;">
                        <h6>🤖 CAPTCHA Verification</h6>
                        <p><strong>Public Key:</strong> ${checkpointInfo.public_key}</p>
                        <p><strong>Instructions:</strong> This appears to be a reCAPTCHA. You may need to:</p>
                        <ol style="text-align: left; margin: 10px 0;">
                            <li>Open LinkedIn in a separate browser tab</li>
                            <li>Complete the CAPTCHA verification there</li>
                            <li>Return here and try connecting again</li>
                        </ol>
                        <p><em>Or enter the CAPTCHA response token if you have it:</em></p>
                    </div>
                `;
            } else {
                captchaContainer.innerHTML = `
                    <div style="border: 2px solid #ddd; padding: 20px; border-radius: 8px; background: #f8f9fa;">
                        <h6>🤖 CAPTCHA Verification Required</h6>
                        <p>LinkedIn requires CAPTCHA verification to complete the connection.</p>
                        <p><strong>Recommended approach:</strong></p>
                        <ol style="text-align: left; margin: 10px 0;">
                            <li>Open <a href="https://www.linkedin.com/login" target="_blank">LinkedIn Login</a> in a new tab</li>
                            <li>Log in with the same credentials</li>
                            <li>Complete any CAPTCHA verification</li>
                            <li>Return here and try connecting again</li>
                        </ol>
                    </div>
                `;
            }
        }

        function refreshCaptcha() {
            if (checkpointData && checkpointData.checkpoint) {
                loadCaptcha(checkpointData.checkpoint);
            }
        }

        function solveCheckpoint() {
            const code = document.getElementById('verificationCode').value;
            const captchaResponse = document.getElementById('captchaResponse').value;
            const resultDiv = document.getElementById('checkpointResult');

            // Determine which input to use based on checkpoint type
            let verificationValue = '';
            if (checkpointData && checkpointData.checkpoint_type === 'CAPTCHA') {
                verificationValue = captchaResponse;
                if (!verificationValue) {
                    resultDiv.innerHTML = '<div class="error">❌ Please complete the CAPTCHA verification</div>';
                    return;
                }
            } else {
                verificationValue = code;
                if (!verificationValue) {
                    resultDiv.innerHTML = '<div class="error">❌ Please enter the verification code</div>';
                    return;
                }
            }

            if (!checkpointAccountId) {
                resultDiv.innerHTML = '<div class="error">❌ No checkpoint session found. Please try connecting again.</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">🔄 Verifying...</div>';

            fetch('/api/linkedin/solve-checkpoint', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    account_id: checkpointAccountId,
                    code: verificationValue,
                    checkpoint_type: checkpointData ? checkpointData.checkpoint_type : 'unknown'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Verification successful!<br>
                            ${data.message}<br>
                            ${data.account_id ? `Account ID: ${data.account_id}` : ''}
                        </div>
                    `;
                    // Hide checkpoint section
                    setTimeout(() => {
                        document.getElementById('checkpointSection').style.display = 'none';
                        document.getElementById('verificationCode').value = '';
                        checkpointAccountId = null;
                    }, 2000);
                    // Refresh status
                    setTimeout(checkUnipileStatus, 3000);
                } else if (data.checkpoint_required) {
                    // Another checkpoint required
                    showCheckpointSection(data.checkpoint_type, data.message);
                    resultDiv.innerHTML = `
                        <div class="info">
                            🔐 Additional verification required<br>
                            ${data.message}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Verification failed: ' + (data.detail || data.error || data.message || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Verification error: ' + error.message + '</div>';
            });
        }

        function sendConnectionRequest() {
            const personId = document.getElementById('connectionPersonId').value;
            const message = document.getElementById('connectionMessage').value;
            const resultDiv = document.getElementById('connectionResult');

            if (!personId || !message) {
                resultDiv.innerHTML = '<div class="error">❌ Please fill in all fields</div>';
                return;
            }

            // First check if LinkedIn account is connected
            resultDiv.innerHTML = '<div class="info">🔄 Checking LinkedIn account connection...</div>';

            fetch('/api/linkedin/unipile/status')
            .then(response => response.json())
            .then(statusData => {
                if (!statusData.success || !statusData.accounts || statusData.accounts.length === 0) {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ No LinkedIn account connected!<br>
                            <strong>Please connect your LinkedIn account first:</strong><br>
                            1. Enter your LinkedIn credentials above<br>
                            2. Click "Connect LinkedIn Account"<br>
                            3. Complete any verification steps<br>
                            4. Wait for account to appear in "Connection Status"<br>
                            5. Then try sending the connection request again
                        </div>
                    `;
                    return;
                }

                // Account is connected, proceed with sending connection request
                resultDiv.innerHTML = '<div class="info">🔄 Sending connection request...</div>';

                fetch('/api/linkedin/send-connection-message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        recipient_id: personId,
                        message: message
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">
                                ✅ Connection request sent successfully!<br>
                                <small>Method: ${data.method || 'Unipile'}</small>
                            </div>
                        `;
                    } else {
                        // Provide specific error handling
                        let errorMessage = data.detail || data.error || 'Unknown error';
                        let helpText = '';

                        if (errorMessage.includes('Person ID not configured')) {
                            helpText = '<br><strong>Solution:</strong> Make sure your LinkedIn account is connected via Unipile above.';
                        } else if (errorMessage.includes('Failed to get profile')) {
                            helpText = '<br><strong>Solution:</strong> Check that the Person ID is correct. Use either:<br>• LinkedIn provider ID (e.g., ACoAAABCDEF)<br>• Public identifier from LinkedIn URL (e.g., john-doe)';
                        } else if (errorMessage.includes('No LinkedIn account_id available')) {
                            helpText = '<br><strong>Solution:</strong> Connect your LinkedIn account first using the form above.';
                        }

                        resultDiv.innerHTML = `
                            <div class="error">
                                ❌ Failed to send connection request<br>
                                <strong>Error:</strong> ${errorMessage}${helpText}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ Connection error: ${error.message}<br>
                            <strong>Tip:</strong> Make sure the server is running and try again.
                        </div>
                    `;
                });
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Failed to check account status: ${error.message}<br>
                        <strong>Tip:</strong> Make sure the server is running and try again.
                    </div>
                `;
            });
        }
        
        function sendInMail() {
            const recipientId = document.getElementById('inmailRecipientId').value;
            const subject = document.getElementById('inmailSubject').value;
            const message = document.getElementById('inmailMessage').value;
            const resultDiv = document.getElementById('inmailResult');

            if (!recipientId || !subject || !message) {
                resultDiv.innerHTML = '<div class="error">❌ Please fill in all fields</div>';
                return;
            }

            // Show loading state
            resultDiv.innerHTML = '<div class="info">🔄 Sending InMail...</div>';

            fetch('/api/linkedin/send-inmail', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: recipientId,
                    subject: subject,
                    message_body: message
                })
            })
            .then(response => {
                // Handle both successful and error responses
                if (response.ok) {
                    return response.json();
                } else {
                    return response.json().then(errorData => {
                        throw new Error(errorData.detail || errorData.error || `HTTP ${response.status}`);
                    });
                }
            })
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ InMail sent successfully!</div>';
                } else {
                    // Handle case where response is 200 but success is false
                    const errorMsg = data.error || data.detail || 'Unknown error';
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send: ' + errorMsg + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Failed to send: ' + error.message + '</div>';
            });
        }

        function searchProfiles() {
            const query = document.getElementById('profileSearchQuery').value;
            const resultDiv = document.getElementById('profileSearchResults');

            if (!query || query.trim().length < 2) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter at least 2 characters to search</div>';
                return;
            }

            // Show loading state
            resultDiv.innerHTML = '<div class="info">🔍 Searching LinkedIn profiles...</div>';

            fetch('/api/linkedin/search-profiles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: query.trim(),
                    limit: 10
                })
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    return response.json().then(errorData => {
                        throw new Error(errorData.detail || errorData.error || `HTTP ${response.status}`);
                    });
                }
            })
            .then(data => {
                if (data.success && data.data && data.data.profiles && data.data.profiles.length > 0) {
                    const profiles = data.data.profiles;
                    let html = `<div class="success">✅ Found ${profiles.length} profiles:</div>`;
                    html += '<div style="margin-top: 10px;">';

                    profiles.forEach((profile, index) => {
                        const profileId = profile.provider_id || profile.id || profile.public_identifier;
                        const profileName = profile.name || 'Unknown';
                        const headline = profile.headline || '';

                        html += `
                            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 10px; margin: 5px 0; cursor: pointer;"
                                 onclick="selectProfile('${profileId}', '${profileName}')">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <strong style="color: #0077b5;">${profileName}</strong>
                                        ${headline ? `<br><small style="color: #666;">${headline}</small>` : ''}
                                        <br><small style="color: #888;">ID: ${profileId}</small>
                                    </div>
                                    <button type="button" style="background: #0077b5; color: white; border: none; padding: 5px 10px; border-radius: 4px; font-size: 12px;">
                                        Select
                                    </button>
                                </div>
                            </div>
                        `;
                    });

                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="info">ℹ️ No profiles found for "${query}". Try a different search term.</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Profile search failed: ' + error.message + '</div>';
            });
        }

        function selectProfile(profileId, profileName) {
            // Fill the InMail recipient field with the selected profile
            document.getElementById('inmailRecipientId').value = profileId;

            // Show confirmation
            const resultDiv = document.getElementById('profileSearchResults');
            resultDiv.innerHTML = `<div class="success">✅ Selected profile: ${profileName} (${profileId})</div>`;

            // Scroll to InMail section
            document.getElementById('inmailRecipientId').scrollIntoView({ behavior: 'smooth' });
            document.getElementById('inmailRecipientId').focus();
        }

        function sendSmartInMail() {
            const recipientId = document.getElementById('inmailRecipientId').value;
            const subject = document.getElementById('inmailSubject').value;
            const message = document.getElementById('inmailMessage').value;
            const resultDiv = document.getElementById('inmailResult');

            if (!recipientId || !subject || !message) {
                resultDiv.innerHTML = '<div class="error">❌ Please fill in all fields for Smart InMail</div>';
                return;
            }

            // Show loading state
            resultDiv.innerHTML = '<div class="info">🧠 Sending Smart InMail (searching profiles and trying multiple approaches)...</div>';

            fetch('/api/linkedin/smart-inmail', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: recipientId,  // Can be name, identifier, or search query
                    subject: subject,
                    message_body: message
                })
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    return response.json().then(errorData => {
                        throw new Error(errorData.detail || errorData.error || `HTTP ${response.status}`);
                    });
                }
            })
            .then(data => {
                if (data.success) {
                    let html = '<div class="success">🎉 Smart InMail sent successfully!</div>';

                    // Show details about how it was sent
                    const result = data.result;
                    if (result.method === 'smart_search') {
                        html += '<div class="info"><h5>📊 Smart Search Results:</h5>';
                        html += `<p><strong>Search Query:</strong> ${result.search_query}</p>`;
                        html += `<p><strong>Profiles Searched:</strong> ${result.profiles_searched}</p>`;
                        if (result.recipient_found) {
                            html += `<p><strong>Recipient Found:</strong> ${result.recipient_found.name || 'Unknown'}</p>`;
                        }
                        html += '</div>';
                    }

                    resultDiv.innerHTML = html;
                } else {
                    const errorMsg = data.error || data.detail || 'Unknown error';
                    resultDiv.innerHTML = '<div class="error">❌ Smart InMail failed: ' + errorMsg + '</div>';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Smart InMail failed: ' + error.message + '</div>';
            });
        }

        function debugInMail() {
            const recipientId = document.getElementById('inmailRecipientId').value;
            const subject = document.getElementById('inmailSubject').value;
            const message = document.getElementById('inmailMessage').value;
            const resultDiv = document.getElementById('inmailResult');

            if (!recipientId || !subject || !message) {
                resultDiv.innerHTML = '<div class="error">❌ Please fill in all fields for debugging</div>';
                return;
            }

            // Show loading state
            resultDiv.innerHTML = '<div class="info">🔍 Running InMail debug...</div>';

            fetch('/api/linkedin/debug-inmail', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipient_id: recipientId,
                    subject: subject,
                    message_body: message
                })
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    return response.json().then(errorData => {
                        throw new Error(errorData.detail || errorData.error || `HTTP ${response.status}`);
                    });
                }
            })
            .then(data => {
                let html = '<div class="info"><h4>🔍 InMail Debug Results</h4>';

                // Debug info
                if (data.debug_info) {
                    html += '<h5>📊 Debug Information:</h5><ul>';
                    for (const [key, value] of Object.entries(data.debug_info)) {
                        html += `<li><strong>${key}:</strong> ${value}</li>`;
                    }
                    html += '</ul>';
                }

                // Result
                if (data.result) {
                    html += '<h5>📧 InMail Result:</h5>';
                    if (data.result.success) {
                        html += '<div class="success">✅ InMail sent successfully!</div>';
                    } else {
                        html += '<div class="error">❌ InMail failed:</div>';
                        html += `<pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; margin: 10px 0;">${JSON.stringify(data.result, null, 2)}</pre>`;
                    }
                }

                html += '</div>';
                resultDiv.innerHTML = html;
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="error">❌ Debug failed: ' + error.message + '</div>';
            });
        }

        function sendCompanyMessage() {
            const companyId = document.getElementById('companyId').value;
            const recipientId = document.getElementById('companyRecipientId').value;
            const message = document.getElementById('companyMessage').value;
            
            if (!companyId || !recipientId || !message) {
                document.getElementById('companyResult').innerHTML = 
                    '<div class="error">❌ Please fill in all fields</div>';
                return;
            }
            
            fetch('/api/linkedin/send-company-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    company_id: companyId,
                    recipient_id: recipientId,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('companyResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Company message sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('companyResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        // Auto-check status on page load
        window.onload = function() {
            checkUnipileStatus();
        };

        // Chat History Functions
        function loadConversations() {
            const resultDiv = document.getElementById('chatHistoryResult');
            resultDiv.innerHTML = '<div class="info">🔄 Loading conversations...</div>';

            fetch('/api/linkedin/conversations?limit=50')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    displayConversations(data.data);
                    resultDiv.innerHTML = `<div class="success">✅ Loaded ${data.data.length} conversations</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.detail || data.error || 'Failed to load conversations'}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            });
        }

        function loadInMailHistory() {
            const resultDiv = document.getElementById('chatHistoryResult');
            resultDiv.innerHTML = '<div class="info">🔄 Loading InMail history...</div>';

            fetch('/api/linkedin/inmail-history?limit=50')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    displayInMailHistory(data.data);
                    resultDiv.innerHTML = `<div class="success">✅ Loaded ${data.data.length} InMail messages</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.detail || data.error || 'Failed to load InMail history'}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            });
        }

        function showSearchInterface() {
            const searchInterface = document.getElementById('searchInterface');
            searchInterface.style.display = searchInterface.style.display === 'none' ? 'block' : 'none';
        }

        function searchChatHistory() {
            const query = document.getElementById('searchQuery').value;
            const resultDiv = document.getElementById('searchResults');

            if (!query || query.trim().length < 2) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter at least 2 characters to search</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">🔄 Searching chat history...</div>';

            fetch(`/api/linkedin/search-chat?query=${encodeURIComponent(query.trim())}&limit=50`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    displaySearchResults(data.data, query);
                    resultDiv.innerHTML = `<div class="success">✅ Found ${data.data.length} matching messages</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.detail || data.error || 'Search failed'}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            });
        }

        function displayConversations(conversations) {
            const container = document.getElementById('conversationsContainer');
            const listDiv = document.getElementById('conversationsList');

            if (!conversations || conversations.length === 0) {
                container.innerHTML = '<div class="info">No conversations found</div>';
                listDiv.style.display = 'block';
                return;
            }

            let html = '';
            conversations.forEach(conv => {
                const lastMessage = conv.last_message || {};
                const participants = conv.participants || [];
                const participantNames = participants.map(p => p.name || p.username || 'Unknown').join(', ');

                html += `
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin: 10px 0; cursor: pointer;"
                         onclick="loadChatMessages('${conv.id}', '${conv.name || participantNames}')">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <h5 style="margin: 0; color: #0077b5;">${conv.name || participantNames}</h5>
                                <p style="margin: 5px 0; color: #666; font-size: 14px;">
                                    ${lastMessage.text ? lastMessage.text.substring(0, 100) + (lastMessage.text.length > 100 ? '...' : '') : 'No recent messages'}
                                </p>
                            </div>
                            <div style="text-align: right; color: #888; font-size: 12px;">
                                <div>${conv.message_count || 0} messages</div>
                                <div>${lastMessage.timestamp ? new Date(lastMessage.timestamp).toLocaleDateString() : ''}</div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
            listDiv.style.display = 'block';

            // Hide other containers
            document.getElementById('chatMessagesContainer').style.display = 'none';
            document.getElementById('inmailHistoryContainer').style.display = 'none';
        }

        function loadChatMessages(chatId, chatName) {
            const resultDiv = document.getElementById('chatHistoryResult');
            resultDiv.innerHTML = '<div class="info">🔄 Loading messages...</div>';

            fetch(`/api/linkedin/chat/${encodeURIComponent(chatId)}/messages?limit=50`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    displayChatMessages(data.data, chatName);
                    resultDiv.innerHTML = `<div class="success">✅ Loaded ${data.data.length} messages</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ ${data.detail || data.error || 'Failed to load messages'}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            });
        }

        function displayChatMessages(messages, chatName) {
            const container = document.getElementById('messagesContainer');
            const chatContainer = document.getElementById('chatMessagesContainer');
            const titleElement = document.getElementById('chatTitle');

            titleElement.textContent = `💬 ${chatName || 'Chat Messages'}`;

            if (!messages || messages.length === 0) {
                container.innerHTML = '<div class="info">No messages found in this conversation</div>';
                chatContainer.style.display = 'block';
                return;
            }

            // Sort messages by timestamp (oldest first for chat flow)
            messages.sort((a, b) => new Date(a.timestamp || 0) - new Date(b.timestamp || 0));

            let html = '';
            messages.forEach(msg => {
                const isFromMe = msg.is_from_me;
                const sender = msg.sender || {};
                const senderName = sender.name || sender.username || (isFromMe ? 'You' : 'Contact');
                const timestamp = msg.timestamp ? new Date(msg.timestamp).toLocaleString() : '';

                html += `
                    <div style="margin: 10px 0; padding: 10px; border-radius: 8px; ${isFromMe ? 'background: #e3f2fd; margin-left: 20px;' : 'background: #f5f5f5; margin-right: 20px;'}">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                            <strong style="color: ${isFromMe ? '#1976d2' : '#333'};">${senderName}</strong>
                            <small style="color: #666;">${timestamp}</small>
                        </div>
                        <div style="color: #333; line-height: 1.4;">
                            ${msg.text || 'No text content'}
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
            chatContainer.style.display = 'block';

            // Hide other containers
            document.getElementById('conversationsList').style.display = 'none';
            document.getElementById('inmailHistoryContainer').style.display = 'none';

            // Scroll to bottom
            container.scrollTop = container.scrollHeight;
        }

        function displayInMailHistory(inmailHistory) {
            const container = document.getElementById('inmailContainer');
            const inmailDiv = document.getElementById('inmailHistoryContainer');

            if (!inmailHistory || inmailHistory.length === 0) {
                container.innerHTML = '<div class="info">No InMail history found</div>';
                inmailDiv.style.display = 'block';
                return;
            }

            let html = '';
            inmailHistory.forEach(item => {
                const message = item.message || {};
                const conversation = item.conversation_name || 'Unknown Conversation';
                const participants = item.participants || [];
                const isFromMe = message.is_from_me;
                const timestamp = message.timestamp ? new Date(message.timestamp).toLocaleString() : '';

                html += `
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin: 10px 0;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h5 style="margin: 0; color: #0077b5;">${conversation}</h5>
                            <span style="color: #666; font-size: 12px;">${timestamp}</span>
                        </div>
                        <div style="background: ${isFromMe ? '#e3f2fd' : '#f5f5f5'}; padding: 10px; border-radius: 6px;">
                            <div style="font-weight: bold; margin-bottom: 5px; color: ${isFromMe ? '#1976d2' : '#333'};">
                                ${isFromMe ? 'You' : 'Contact'}
                            </div>
                            <div style="color: #333; line-height: 1.4;">
                                ${message.text || 'No text content'}
                            </div>
                        </div>
                        ${participants.length > 0 ? `<div style="margin-top: 10px; font-size: 12px; color: #666;">Participants: ${participants.map(p => p.name || p.username || 'Unknown').join(', ')}</div>` : ''}
                    </div>
                `;
            });

            container.innerHTML = html;
            inmailDiv.style.display = 'block';

            // Hide other containers
            document.getElementById('conversationsList').style.display = 'none';
            document.getElementById('chatMessagesContainer').style.display = 'none';
        }

        function displaySearchResults(searchResults, query) {
            const container = document.getElementById('searchResults');

            if (!searchResults || searchResults.length === 0) {
                container.innerHTML = `<div class="info">No messages found containing "${query}"</div>`;
                return;
            }

            let html = `<h5>🔍 Search Results for "${query}"</h5>`;
            searchResults.forEach(result => {
                const message = result.message || {};
                const conversation = result.conversation || {};
                const timestamp = message.timestamp ? new Date(message.timestamp).toLocaleString() : '';
                const isFromMe = message.is_from_me;

                // Highlight the search query in the text
                let highlightedText = message.text || '';
                const regex = new RegExp(`(${query})`, 'gi');
                highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');

                html += `
                    <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin: 10px 0;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h6 style="margin: 0; color: #0077b5;">${conversation.name || 'Unknown Conversation'}</h6>
                            <span style="color: #666; font-size: 12px;">${timestamp}</span>
                        </div>
                        <div style="background: ${isFromMe ? '#e3f2fd' : '#f5f5f5'}; padding: 10px; border-radius: 6px;">
                            <div style="font-weight: bold; margin-bottom: 5px; color: ${isFromMe ? '#1976d2' : '#333'};">
                                ${isFromMe ? 'You' : 'Contact'}
                            </div>
                            <div style="color: #333; line-height: 1.4;">
                                ${highlightedText}
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function closeChatView() {
            document.getElementById('chatMessagesContainer').style.display = 'none';
            document.getElementById('conversationsList').style.display = 'block';
        }
    </script>
</body>
</html>
