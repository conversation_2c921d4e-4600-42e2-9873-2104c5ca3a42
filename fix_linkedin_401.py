#!/usr/bin/env python3
"""
Quick fix for LinkedIn 401 Unauthorized error
"""

import json
import os

def fix_linkedin_api_key():
    """Fix the LinkedIn API key configuration"""
    
    print("🔧 Fixing LinkedIn 401 Unauthorized Error")
    print("=" * 60)
    
    # Correct API key
    correct_api_key = "0wYBrLyQ.2oA9eegTBThrd+sGgO1scm8q25M9pqIVY5Xn2VfHs9M="
    
    # Files to update
    config_files = [
        "integrations/linkedin_integration/config.json",
        "integrations/unified_config.json",
        "integrations/unified_messaging_config.json"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                print(f"📄 Updating {config_file}...")
                
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # Update API key in different possible locations
                updated = False
                
                if "unipile_api_key" in config:
                    if config["unipile_api_key"] != correct_api_key:
                        config["unipile_api_key"] = correct_api_key
                        updated = True
                        print(f"   ✅ Updated unipile_api_key")
                
                if "unipile" in config and isinstance(config["unipile"], dict):
                    if config["unipile"].get("api_key") != correct_api_key:
                        config["unipile"]["api_key"] = correct_api_key
                        updated = True
                        print(f"   ✅ Updated unipile.api_key")
                
                if "unipile_config" in config and isinstance(config["unipile_config"], dict):
                    if config["unipile_config"].get("api_key") != correct_api_key:
                        config["unipile_config"]["api_key"] = correct_api_key
                        updated = True
                        print(f"   ✅ Updated unipile_config.api_key")
                
                if "unipile_settings" in config and isinstance(config["unipile_settings"], dict):
                    if config["unipile_settings"].get("api_key") != correct_api_key:
                        config["unipile_settings"]["api_key"] = correct_api_key
                        updated = True
                        print(f"   ✅ Updated unipile_settings.api_key")
                
                if updated:
                    with open(config_file, 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=2, ensure_ascii=False)
                    print(f"   💾 Saved {config_file}")
                else:
                    print(f"   ℹ️ No updates needed for {config_file}")
                    
            except Exception as e:
                print(f"   ❌ Error updating {config_file}: {e}")
        else:
            print(f"📄 {config_file}: File not found")
    
    print("\n🔑 API Key Information:")
    print(f"   Correct API Key: {correct_api_key[:20]}...{correct_api_key[-10:]}")
    print(f"   Base URL: https://api14.unipile.com:14455")

def create_test_script():
    """Create a simple test script to verify the fix"""
    
    test_script = '''#!/usr/bin/env python3
"""
Test LinkedIn API key after fix
"""

import requests
import json

def test_api_key():
    api_key = "0wYBrLyQ.2oA9eegTBThrd+sGgO1scm8q25M9pqIVY5Xn2VfHs9M="
    base_url = "https://api14.unipile.com:14455/api/v1"
    
    headers = {
        "X-API-KEY": api_key,
        "accept": "application/json"
    }
    
    try:
        print("🧪 Testing API key...")
        response = requests.get(f"{base_url}/accounts", headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ API key is working!")
            data = response.json()
            accounts = data.get("items", [])
            print(f"📱 Found {len(accounts)} accounts")
            return True
        elif response.status_code == 401:
            print("❌ 401 Unauthorized - API key is invalid")
            return False
        else:
            print(f"⚠️ Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_api_key()
'''
    
    with open("test_linkedin_fix.py", "w") as f:
        f.write(test_script)
    
    print("📝 Created test_linkedin_fix.py")

def display_instructions():
    """Display instructions for fixing the issue"""
    
    print("\n📋 Instructions to Fix LinkedIn 401 Error:")
    print("=" * 60)
    
    print("1. 🔧 Configuration Updated:")
    print("   - API key has been updated in config files")
    print("   - Using correct Unipile API endpoint")
    
    print("\n2. 🔄 Restart Required:")
    print("   - Stop the current server if running")
    print("   - Restart with: python integrations/api_endpoints.py")
    
    print("\n3. 🧪 Test the Fix:")
    print("   - Run: python test_linkedin_fix.py")
    print("   - Should show '✅ API key is working!'")
    
    print("\n4. 🌐 Try LinkedIn Integration:")
    print("   - Access: http://localhost:8000/linkedin/linkedin_auth.html")
    print("   - Enter your credentials: <EMAIL>")
    print("   - Should work without 401 error")
    
    print("\n5. 🔐 Handle CAPTCHA (if needed):")
    print("   - If you get CAPTCHA checkpoint again")
    print("   - Use the enhanced CAPTCHA interface")
    print("   - Account ID: M2T8Q-6ySZ-HQ0YVUN6nAg")

if __name__ == "__main__":
    print("🚀 LinkedIn 401 Error Fix")
    print("=" * 60)
    
    fix_linkedin_api_key()
    create_test_script()
    display_instructions()
    
    print("\n✅ Fix completed!")
    print("🔄 Please restart the server and test the LinkedIn integration.")
